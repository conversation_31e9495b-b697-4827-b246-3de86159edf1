import * as VTable from "@visactor/vtable";

const CONTAINER_ID = "tableContainer";
let tableInstance: VTable.ListTable;
fetch(
  "https://lf9-dp-fe-cms-tos.byteorg.com/obj/bit-cloud/VTable/North_American_Superstore_data.json"
)
  .then((res) => res.json())
  .then((data) => {
    const columns = [
      {
        field: "Order ID",
        title: "Order ID",
        width: "auto",
      },
      {
        field: "Customer ID",
        title: "Customer ID",
        width: "auto",
      },
      {
        field: "Product Name",
        title: "Product Name",
        width: "auto",
      },
      {
        field: "Category",
        title: "Category",
        width: "auto",
      },
      {
        field: "Sub-Category",
        title: "Sub-Category",
        width: "auto",
      },
      {
        field: "Region",
        title: "Region",
        width: "auto",
      },
      {
        field: "City",
        title: "City",
        width: "auto",
      },
      {
        field: "Order Date",
        title: "Order Date",
        width: "auto",
      },
      {
        field: "Quantity",
        title: "Quantity",
        width: "auto",
      },
      {
        field: "Sales",
        title: "Sales",
        width: "auto",
      },
      {
        field: "Profit",
        title: "Profit",
        width: "auto",
      },
    ];

    const option = {
      records: data,
      columns,
      widthMode: "standard" as const,
    };
    const container = document.getElementById(CONTAINER_ID);
    if (container) {
      tableInstance = new VTable.ListTable(container, option);
      (window as unknown as Record<string, VTable.ListTable>)["tableInstance"] =
        tableInstance;
    }
  });
